import React, { useEffect } from 'react';
import { Form, Input, Select, Card, Row, Col, Button } from 'antd';
import type { FormInstance } from 'antd';

import type { AlertSend } from '../../types';
import { formStyles } from '../../styles';
import { FORM_BUTTON_TEXT } from '../../constants';

const { Option } = Select;

interface AlertSendBasicFormProps {
  form: FormInstance;
  initialData?: AlertSend;
  onSubmit?: (values: any) => void;
  onCancel?: () => void;
  onReset?: () => void;
  loading?: boolean;
}

/**
 * 告警发送基本信息表单组件
 * 用于新增和编辑告警发送配置
 */
const AlertSendBasicForm: React.FC<AlertSendBasicFormProps> = ({ form, initialData, onSubmit, onCancel, onReset, loading = false }) => {
  // 监听表单值变化，动态显示不同类型的配置
  const typeValue = Form.useWatch('receive_type', form);

  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    }
  }, [form, initialData]);

  // 表单提交处理
  const handleSubmit = (values: AlertSend) => {
    console.log('告警发送表单提交:', values);
    onSubmit?.(values);
  };

  // 按钮点击提交处理
  const handleButtonSubmit = () => {
    form.submit();
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  return (
    <div className="h-full flex flex-col">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className={formStyles.form}
        initialValues={{
          receive_type: 'kafka',
        }}
      >
        {/* 隐藏的 id 字段，用于编辑时保持 id */}
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>
        <div className={formStyles.tabContent}>
          <Card title="基本信息" className={formStyles.card}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="发送名称"
                  rules={[
                    { required: true, message: '请输入发送名称' },
                    { max: 50, message: '发送名称不能超过50个字符' },
                  ]}
                >
                  <Input placeholder="请输入发送名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="receive_type" label="接收类型" rules={[{ required: true, message: '请选择接收类型' }]}>
                  <Select placeholder="请选择接收类型">
                    <Option value="kafka">Kafka</Option>
                    <Option value="prometheus">Prometheus</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* Kafka 配置 */}
          {typeValue === 'kafka' && (
            <Card title="Kafka 配置" className={formStyles.card}>
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    name={['kafka_receiver', 'address']}
                    label="Kafka 地址"
                    rules={[
                      { required: true, message: '请输入Kafka地址' },
                      {
                        pattern: /^[\w.-]+:\d+(,[\w.-]+:\d+)*$/,
                        message: '请输入正确的地址格式，如：host1:port1,host2:port2',
                      },
                    ]}
                  >
                    <Input placeholder="请输入Kafka地址，格式：host1:port1,host2:port2" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name={['kafka_receiver', 'topic']} label="Topic" rules={[{ required: true, message: '请输入Topic' }]}>
                    <Input placeholder="请输入Topic" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name={['kafka_receiver', 'username']} label="用户名">
                    <Input placeholder="请输入用户名（可选）" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name={['kafka_receiver', 'password']} label="密码">
                    <Input.Password placeholder="请输入密码（可选）" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}

          {/* Prometheus 配置 */}
          {typeValue === 'prometheus' && (
            <Card title="Prometheus 配置" className={formStyles.card}>
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item
                    name={['prometheus_receiver', 'address']}
                    label="Prometheus 地址"
                    rules={[
                      { required: true, message: '请输入Prometheus地址' },
                      {
                        pattern: /^[\w.-]+:\d+(,[\w.-]+:\d+)*$/,
                        message: '请输入正确的地址格式，如：host1:port1,host2:port2',
                      },
                    ]}
                  >
                    <Input placeholder="请输入Prometheus地址，格式：host1:port1,host2:port2" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['prometheus_receiver', 'username']} label="用户名">
                    <Input placeholder="请输入用户名（可选）" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={['prometheus_receiver', 'password']} label="密码">
                    <Input.Password placeholder="请输入密码（可选）" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}
        </div>
      </Form>
      {/* 操作按钮 */}
      <div className={formStyles.footerContainer}>
        <div className="flex justify-between items-center">
          <div className={formStyles.footerHint}>{initialData ? '编辑告警发送' : '创建新告警发送'}</div>
          <div className={formStyles.buttonGroup}>
            <Button onClick={onCancel} className={`${formStyles.actionButton} ${formStyles.cancelButton}`}>
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            {!initialData && (
              <Button onClick={handleReset} className={`${formStyles.actionButton} ${formStyles.resetButton}`}>
                {FORM_BUTTON_TEXT.reset}
              </Button>
            )}
            <Button type="primary" loading={loading} onClick={handleButtonSubmit} className={`${formStyles.actionButton} ${initialData ? formStyles.confirmButton : formStyles.submitButton}`}>
              {initialData ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlertSendBasicForm;
